# Data Analyst Assignment - July 2025

## Part 1 - Understanding Siyavula Practice

### Task

Register on [www.siyavula.com](http://www.siyavula.com) - you can pick "None" as a school and choose grade 8. Then practice the first section of Grade 8 Whole Numbers and try to get 4 stars.

### Submission

Send us your Siyavula ID, which you can find on your My Account page. See how to find your Siyavula ID.

---

## Part 2 - Practice Patterns

On your My Account page, you can find a question history. Having completed Part 1, you should recognize the data in your question history and review the column names as fields that would be stored across various database tables.

An anonymized dataset was created from a number of database tables as part of a research project - it's hosted on Figshare here. This data enables an analysis of users’ practice behavior. It comes with a README so that someone external to Siyavula can work with it, and it has a set of CSVs with some common fields (to a degree, simulating a set of linked database tables).

### Task

- Use some of the anonymized dataset linked above.
- Please create a Jupyter notebook, using Python 3.8, to:
  - Do some initial exploratory data analysis so that you are comfortable with the data.
  - Investigate typical trajectories of mastery vs questions attempted for all sections.
  - Identify potentially problematic sections.
- Prepare the notebook so that it can be used to explain your thinking to other team members in general, and also to justify to the pedagogy team which sections they should review and why.
- Ensure your notebook includes information related to the versions of any specific libraries you use.

### Submission

Send us your saved notebook, including all of your notes and output.

---

## Part 3 - Assisting Commercial Team Members

The Siyavula Foundation has additional packages that are sold to institutions of learning. The commercial team is looking for some help to analyze their sales trends over the last four years. They asked an AI to help them anonymize their data, which is tracked in Google Sheets, so that they could share it. The resulting CSV files can be found in this Google Drive folder: **Data Analyst - Fake Commercial Data - July 2025**.

They provided these contextual notes:

- Over the last 4 years, the package names have changed.
- Schools can be public or private, and either type of school might focus on writing the government exams, National Senior Certificate (NSC), or the Independent Examinations Body (IEB) exams. The national curriculum is called CAPS. Teachers often talk about being either CAPS or IEB - CAPS -P is for Public schools, and CAPS-i is for independent schools, IEB. Tutoring companies are indicated as N/A.
- A sales cycle runs from July in one year to June in the following year because purchasing decisions are usually made when annual budgets are set, but sometimes schools can still buy early in a school year.

### Task

- Please review the pricing page for teachers so that you have some context for the way the packages are priced and the current package names.
- Use the anonymized CSV files provided by the commercial team.
- Please create a Jupyter notebook, using Python 3.8, to:
  - Do some initial exploratory data analysis so that you are comfortable with the data.
  - Analyze sales cycle trends and retention rates by organization characteristics.
  - Identify areas where the customer success team can focus to improve retention, and the sales team can focus to improve growth.
  - Prepare data visualizations to support your conclusions.
- Prepare the notebook so that it can be used by other members of the Product team to understand your analysis process and conclusions, as well as reproduce the analysis if necessary.
- Ensure your notebook includes information related to the versions of any specific libraries you use.
- Please also prepare a short set of slides in which you:
  - Summarize your analysis
  - Present your data visualizations related to trends and retention
  - Summarize your recommendations related to retention and sales focus areas.
  - Motivate for three ways sales data and data from the platform might be integrated in the future to give the Commercial team a proper client dashboard.

### Submission

Send us your:

- Saved notebook, including all of your notes and output.
- Slides for discussion with the Commercial team.
