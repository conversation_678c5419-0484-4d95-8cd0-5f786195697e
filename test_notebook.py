#!/usr/bin/env python3
"""
Test script to verify the commercial sales analysis notebook will work
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import os

def test_notebook_functionality():
    """Test that all components needed for the notebook work"""
    
    print("=== TESTING NOTEBOOK FUNCTIONALITY ===")
    
    # Test 1: Library imports
    try:
        warnings.filterwarnings('ignore')
        plt.style.use('default')
        print("✅ All required libraries imported successfully")
    except Exception as e:
        print(f"❌ Library import error: {e}")
        return False
    
    # Test 2: Data directory access
    data_dir = 'Data Analyst - Fake Commercial Data - July 2025'
    if os.path.exists(data_dir):
        print("✅ Data directory found")
        
        # Test 3: Excel file access
        excel_files = [f for f in os.listdir(data_dir) if f.endswith('.xlsx')]
        print(f"✅ Found {len(excel_files)} Excel files")
        
        if excel_files:
            # Test 4: Data loading
            try:
                test_file = os.path.join(data_dir, excel_files[0])
                df = pd.read_excel(test_file)
                print(f"✅ Successfully loaded test file: {df.shape}")
                
                # Test 5: Key columns
                required_cols = ['CLIENT', 'Deal Value']
                missing_cols = [col for col in required_cols if col not in df.columns]
                
                if not missing_cols:
                    print("✅ All required columns present")
                    
                    # Test 6: Data processing
                    deals = pd.to_numeric(df['Deal Value'], errors='coerce').dropna()
                    clients = df['CLIENT'].nunique()
                    
                    print(f"✅ Data processing successful:")
                    print(f"   - Valid deals: {len(deals)}")
                    print(f"   - Unique clients: {clients}")
                    print(f"   - Revenue range: R{deals.min():.0f} - R{deals.max():.0f}")
                    
                    return True
                else:
                    print(f"❌ Missing required columns: {missing_cols}")
                    return False
                    
            except Exception as e:
                print(f"❌ Data loading error: {e}")
                return False
        else:
            print("❌ No Excel files found")
            return False
    else:
        print("❌ Data directory not found")
        return False

def main():
    """Main test function"""
    
    success = test_notebook_functionality()
    
    print("\n" + "="*50)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ The commercial sales analysis notebook should work correctly")
        print("✅ All required data and libraries are available")
        print("✅ Data structure is compatible with analysis functions")
    else:
        print("❌ SOME TESTS FAILED!")
        print("⚠️  The notebook may encounter errors")
        print("⚠️  Please check data files and library installations")
    
    print("\n🚀 Ready to run the notebook!")

if __name__ == "__main__":
    main()
