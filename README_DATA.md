# README: DATA

This repository contains the full set of data (14 CSVs) used in the study "Investigating automated prompts to guide at-risk learners in an online practice platform". This study was a minor dissertation by <PERSON> in partial fulfilment of the requirements for the Master of Education (ICTs) from the University of Johannesburg.

Data was collected using the [Siyavula](https://www.siyavula.com/) online adaptive practice platform during the second quarter of 2023. The CSV files in this collection are a set of linked tables containing the data at various points in the analysis pipeline and at different levels of aggregation.

All data sets (and associated code) belong to the [Siyavula Foundation](https://www.siyavulafoundation.org/) and are released under a Creative Commons Attribution 4.0 International [(CC-BY 4.0)](https://creativecommons.org/licenses/by/4.0/) license.

## Data CSVs

The 14 CSVs making up this data set are presented in alphabetical order below. Fields with the same name generally have identical meanings and can generally be used for joins/merges. Any exceptions will be noted and for the sake of brevity, descriptions will not be repeated for identical interpretations.

### all_attempts.csv

All attempts by learners meeting the inclusion criteria from the start of the year 
preceding the period under investigation (2022-01-01).

- `response_uuid`: a unique identifier for the attempt
- `attempted_at`: the timestamp of when the learner made the question attempt
- `user_uuid`: a unique (anonymised and hashed) identifer for the learner
- `section_id`: a uniqe ID for the section on the Siyavula platform 
- `section_type`: can be "revision" or "practice" (for grade-level content)
- `section_mastery`: percentage mastery, as calculated by Siyavula's mastery algorithm
- `activity_type`: questions can be accessed in different modes on the Siyavula platform
- `accuracy`: the learner's performance on an attempt as a fraction from 0 (completely wrong) to 1 (fully correct)
- `nth_attempt_section_all_time`: the cumulative number of attempts by this learner in this section represented by this attempt
- `nth_attempt_overall_all_time`: the cumulative number of attempts by this learner anywhere on the platform represented by this attempt
- `slow_struggle`: (boolean) if the learner would be classed as struggling using the slow timing
- `medium_struggle`: (boolean) if the learner would be classed as struggling using the medium timing
- `fast_struggle`: (boolean) if the learner would be classed as struggling using the aggressive timing
- `persistent`: if the learner did enough questions in this section to meet the inclusion criteria (minimum four attempts)

### attempts.csv

Identical fields to `all_attempts.csv` except for the following additional fields:

- `date_attempted`: the date on which the attempt was made
- `duration`: the time (in seconds) taken by the learner to answer the question (capped at 30 minutes)
- `nth_attempt_section`: the cumulative number of attempts made in the section during the period of the experiment
- `nth_attempt_overall`: the cumulative number of attempts made in any section during the period of the experiment
- `nth_attempt_day`: the cumulative number of attempts made in any section on the given date

### learner_attempts_agg.csv

Attempts data aggregated to the learner level (across all sections).

- `n_attempts`: the total number of attempts made by this learner
- `first_attempted`: the timestamp corresponding to their first attempt
- `n_days_practised`: the total number of unique days on which the learner practised
- `avg_accuracy`: the average accuracy across all attempts
- `total_duration`: the total amount of time spent answering questions
- `mean_duration`: the average time spent per question attempt
- `n_sections`: the total number of different sections in which the learner practised

### learner_prompts_agg.csv

Prompts data aggregated to the learner level (across all sections).

- `n_prompts`: the total number of prompts to revise shown to the learner
- `n_prompts_followed`: the total number of prompts followed by the learner
- `click_rate`: the fraction of prompts followed by the learner (from 0 to 1)
- `first_prompted_at_overall`: the timestamp for when the learner received their first prompt on the system
- `n_sections_prompted`: the number of sections in which the learner was prompted
- `prompted`: if the learner received a prompt at least once
- `prompt_follower`: if the learer followed a prompt at least once

### learner_section_attempts_agg.csv

Attempts data aggregated to the learner-section level. Very similar to `learner_attemps_agg.csv` except for data that only makes sense at the section level. 

- `initial_mastery`: the learner's mastery in the section at the start of the experiment
- `final_mastery`: the final section mastery achieved by the conclusion of the experiment
- `delta_mastery`: the difference between final and initial mastery

### learner_section_prompts_agg.csv

Prompts data aggregated to the learner-sectiokn level. Fields have identical interpretations to `learner_prompts_agg.csv`, except they apply to the section level instead of overall.

- `first_prompted_at_section`: the timestamp for when the learner received their first prompt in the given section

### learner_sectiontype_attempts_agg.csv

Attempts data aggregated to the learner-sectiontype level. Fields have identical interpretations to `learner_attempts_agg.csv` except they apply for a given section type (revision or practice) instead of overall.

### learner_sectiontype_with_masteries_agg.csv

Identical to `learner_sectiontype_attempts_agg.csv`, except for the addition of mastery aggregations (as **sums**) for the given section type.

- `initial_mastery`: the sum of all initial masteries
- `final_mastery`: the sum of all final masteries
- `delta_mastery`: the difference between the sum of all final masteries and the sum of all initial masteries

### learner_with_masteries_agg.csv

Identical to `learner_attempts_agg.csv`, except for the addition of mastery aggregations (as **sums**) for the platform overall. (As with `learner_sectiontype_with_masteries_agg.csv`, but for the platform overall.)

### learners.csv
- `user_uuid`: a unique identifier (anonymised and hashed) for the learner on the platform
- `signed_up_at`: the timestamp for when the learner signed up to Siyavula
- `group`: the experimental group (for prompt timing) the learner was assigned to
- `province`: the South African province where the learner's school is located
- `quintile`: a measure of school wealth
- `master_school_id`: a unique id (anonymised and hashed) for the learner's school
- `new_learner`: (boolean) if the learner signed up after the start of the experiment or not

### prompts.csv

All prompts to revised associated with the learner's question attempts. Note that response IDs in this table will be a subset of those in the attempts table as not all attempts result in prompts.

- `prompted_at`: the timestamp of when the learner received the prompt to revise
- `date_prompted`: the date on which they were prompted
- `section_id`: the section in which they were prompted
- `algorithm_level`: the algorithm level (difficulty bands from 1 to 4) when the prompt was triggered
- `prompt_shown`: if the learner saw the prompt (not shown to learners in the Control group)
- `prompt_followed`: if the learner clicked on the link and followed the prompt
- `nth_prompt_section`: the cumulative number of prompts received in the section
- `nth_prompt_overall`: the cumulative number of prompts received overall
- `nth_prompt_day`: the cumulative number of prompts received on the given date
- `nth_attempt_section`: the cumulative number of questions attempted in the section since the experiment started
- `nth_attempt_overall`: the cumulative number of questions attempted overall since the experiment started
- `nth_attempt_day`: the cumulative number of questions attempted on the given date
- `nth_attempt_section_all_time`: the cumulative number of questions attempted in the section since the learner signed up
- `nth_attempt_overall_all_time`: the cumulative number of questions attempted on the platform since the learner signed up
- `revision_section_id`: the ID of the revision section linked to this grade-level practice section (to which the learner is prompted to go to)
- `nth_revision_attempt_section`: the cumulative number of questions attempted in the linked revision section
- `has_revised`: if the learner has already revised in the linked revision section

### section_mapping.csv
A mapping linking grade-level practice sections (first column) to the ID of the associated revision section (second column). Note that a single revision section can be linked to multiple sections, as there is only one revision section per chapter.

### sections.csv

All sections meeting inclusion criteria and their meta data.

- `section_id`: the uniqe identifier for the section
- `section_type`: revision or practice (grade-level)
- `chapter`: which chapter (number) the section belongs to
- `chapter_name`: the name of the chapter the section belongs to
- `section`: the section number within the chapter
- `section_name`: the name of the section
- `n_questions`: the total number of generative question items within the section
- `n_basic`: the number of basic-level questions in the section
- `n_easy`: the number of easy-level questions in the section
- `n_medium`: the number of medium-level questions in the section
- `n_hard`: the number of hard-level questions in the section
- `avg_difficulty`: the average difficulty of the questions in the section (on a scale of 0-4) 
- `included`: if the section is included in the study (should be True for all rows)

### struggling_learner_sections.csv

A list of learners (first column) who were sufficiently persistent in a given section (second column). The third column should be True for all rows. 

> **NOTE** The CSV is not named well (it's historical) and these learners are not necessarily struggling. They have just done at least four questions in a section in order to be included in the study. 