#!/usr/bin/env python3
"""
Create PowerPoint presentation from Siyavula Commercial Analysis
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE

def create_siyavula_presentation():
    """Create comprehensive PowerPoint presentation"""
    
    # Create presentation
    prs = Presentation()
    
    # Define colors (Siyavula brand-inspired)
    primary_color = RGBColor(0, 102, 204)  # Blue
    secondary_color = RGBColor(255, 102, 0)  # Orange
    text_color = RGBColor(51, 51, 51)  # Dark gray
    
    # Slide 1: Title Slide
    slide = prs.slides.add_slide(prs.slide_layouts[0])
    title = slide.shapes.title
    subtitle = slide.placeholders[1]
    
    title.text = "Siyavula Commercial Sales Analysis"
    subtitle.text = "Data-Driven Insights for Growth and Retention\n\nPresented by: Data Analyst Candidate\nDate: January 2025\nFor: Commercial Team & Leadership"
    
    # Slide 2: Executive Summary
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "Executive Summary"
    content.text = """📊 Analysis Scope
• 4-year sales cycle analysis (2022-2025)
• Customer retention patterns by organization type
• Package performance trends across segments
• Growth opportunities identification

🎯 Key Objectives
• Understand sales trends and growth patterns
• Identify retention challenges and opportunities
• Segment customers by organization characteristics
• Provide actionable recommendations for commercial team"""
    
    # Slide 3: Current Package Structure
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "Current Package Structure"
    content.text = """💰 Pricing Tiers (2025)
• Free Plan: R0 - Basic features for exploration
• Explorer Plan: R169/month or R1,699/year per teacher per subject
• Premium Plan: R549/month or R5,499/year per teacher per subject

🏫 Customer Segments
• Public Schools (CAPS-P): Government curriculum focus
• Independent Schools (CAPS-i/IEB): Private schools, mixed curriculum
• Tutoring Companies (N/A): Private tutoring organizations"""
    
    # Slide 4: Sales Performance - Actual Results
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "Sales Performance - Actual Results (2022-2025)"
    content.text = """📈 Revenue Growth Trajectory
• 2022: R2.34M revenue, 114 clients, R20,895 avg deal
• 2023: R3.37M revenue, 177 clients, R19,243 avg deal (+43.9% growth)
• 2024: R3.65M revenue, 198 clients, R21,620 avg deal (+8.5% growth)
• 2025: R4.74M revenue, 138 clients*, R34,572 avg deal (+29.6% growth)

*Note: 2025 data may be partial year

📊 Key Achievements
• Total 4-year revenue: R14.1 million
• Overall revenue growth: 102.4% (2022-2025)
• Customer base expansion: 73.7% (2022-2024)
• Deal size optimization: 60% increase in 2025"""
    
    # Slide 5: Customer Growth Analysis
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "Customer Growth Analysis"
    content.text = """🔄 Customer Growth Patterns
• 2022-2023: 55% customer growth (114 → 177 clients)
• 2023-2024: 12% customer growth (177 → 198 clients)
• 2024-2025: Customer count appears lower (138)*

📊 Key Insights
• Strong acquisition phase: 2022-2023 exceptional growth
• Stabilization phase: 2023-2024 growth slowed but positive
• Revenue resilience: Despite fewer clients in 2025, revenue +29.6%

⚠️ Areas for Investigation
• 2025 client count: Determine if partial data or churn
• High-value retention: Focus on clients driving larger deals
• Early warning system: Implement proactive monitoring"""
    
    # Slide 6: Organization Segmentation
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "Organization Characteristics Analysis"
    content.text = """🏫 Customer Segments Performance

Public Schools (CAPS-P)
• Government curriculum, budget constraints
• Growing segment, especially in 2025
• High volume potential, lower individual values
• Government budget cycle alignment opportunity

Independent Schools (CAPS-i/IEB)
• Private schools, higher budgets
• Largest customer segment historically
• Higher average deal values
• Premium package adoption success

Tutoring Companies
• Private tutoring, flexible needs
• Niche market with specific requirements
• Variable deal sizes and patterns"""
    
    # Slide 7: Key Recommendations
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "Strategic Recommendations"
    content.text = """🤝 For Customer Success Team
• Investigate 2025 client count discrepancy
• Focus on high-value client retention (R30K+ deals)
• Create segment-specific onboarding programs
• Implement early warning system for at-risk customers

📈 For Sales Team
• Capitalize on deal size momentum from 2025
• Expand public school market presence
• Launch referral programs leveraging satisfied clients
• Align sales cycles with school budget planning

🎯 Priority Focus Areas
• Retention monitoring for high-value accounts
• Public school segment expansion
• Premium package positioning and upselling"""
    
    # Slide 8: Data Integration Opportunities
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "Data Integration Opportunities"
    content.text = """🔗 Three Strategic Integration Proposals

1. Predictive Churn Modeling
   Sales data + Platform usage metrics
   → Identify at-risk customers before renewal
   → Reduce churn by 15-25% through intervention

2. Value Demonstration Dashboard
   Sales data + Student performance outcomes
   → Prove ROI with concrete learning results
   → Increase renewal rates and justify pricing

3. Upselling Intelligence System
   Sales data + Teacher engagement metrics
   → Identify optimal timing for package upgrades
   → Increase average revenue per customer 20-30%"""

    # Slide 9: Implementation Roadmap
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "Implementation Roadmap"
    content.text = """🚀 Phase 1: Foundation (Months 1-2)
• Complete data integration setup
• Implement basic retention tracking
• Launch customer health scoring
• Begin segment-specific outreach

📊 Phase 2: Optimization (Months 3-6)
• Deploy predictive analytics models
• Launch value demonstration dashboards
• Implement automated upselling alerts
• Establish regular business reviews

🎯 Phase 3: Scale (Months 6-12)
• Expand to additional data sources
• Develop advanced segmentation
• Launch customer success automation
• Measure and optimize ROI"""

    # Slide 10: Success Metrics & KPIs
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "Success Metrics & KPIs"
    content.text = """📈 Primary Metrics
• Customer Retention Rate: Target 85%+
• Revenue Growth Rate: Target 25%+ annually
• Customer Lifetime Value: Increase by 30%
• Churn Rate: Reduce to <15%

📊 Secondary Metrics
• Average deal size by segment
• Time to value for new customers
• Feature adoption rates
• Support ticket resolution time

🎯 Business Impact Goals
• Increase annual recurring revenue by 40%
• Improve customer satisfaction scores by 20%
• Reduce customer acquisition cost by 15%
• Expand market share in target segments"""

    # Slide 11: Key Findings Summary
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "Key Findings Summary"
    content.text = """💰 Financial Performance
• 102% revenue growth over 4 years (R2.3M → R4.7M)
• Strong momentum with 29.6% growth in 2025
• Deal size optimization: 60% increase in average value

👥 Customer Insights
• Customer base expanded 74% (2022-2024)
• Independent schools dominate customer mix
• Public school segment shows growth potential
• 2025 client count needs investigation

🎯 Strategic Opportunities
• Premium package success validates pricing strategy
• Public school market expansion opportunity
• Referral program potential from satisfied clients
• Data integration can unlock predictive insights"""

    # Slide 12: Thank You & Next Steps
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "Thank You & Next Steps"
    content.text = """💬 Discussion Points
• Data availability and integration priorities
• Resource allocation for recommendations
• Timeline for implementation phases
• Success measurement framework

📁 Deliverables Provided
• Comprehensive Jupyter notebook with analysis
• This presentation with findings and recommendations
• Detailed implementation roadmap

📧 Contact Information
Data Analyst Candidate
Ready to transform Siyavula's commercial success!

🚀 Next Steps
• Validate 2025 data completeness
• Prioritize retention initiatives
• Begin data integration planning
• Establish regular reporting cadence"""

    return prs

def main():
    """Create and save the PowerPoint presentation"""
    
    print("Creating Siyavula Commercial Analysis PowerPoint presentation...")
    
    try:
        # Create presentation
        prs = create_siyavula_presentation()
        
        # Save presentation
        filename = "Siyavula_Commercial_Analysis_Presentation.pptx"
        prs.save(filename)
        
        print(f"✅ PowerPoint presentation created successfully!")
        print(f"📁 Saved as: {filename}")
        print(f"📊 Contains 12 professional slides with:")
        print("   • Executive summary and objectives")
        print("   • Actual sales performance data (R14.1M total revenue)")
        print("   • Customer growth analysis (102% revenue growth)")
        print("   • Organization segmentation insights")
        print("   • Strategic recommendations for both teams")
        print("   • Data integration opportunities (3 proposals)")
        print("   • Implementation roadmap (3 phases)")
        print("   • Success metrics and KPIs")
        print("   • Key findings summary")
        print("   • Next steps and discussion points")
        print("\n🚀 Ready for presentation to the commercial team!")
        
    except Exception as e:
        print(f"❌ Error creating presentation: {e}")

if __name__ == "__main__":
    main()
