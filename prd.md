# Product Requirements Document: Practice Patterns Analysis

## 1. Overview

This document outlines the requirements for analyzing user practice patterns on the Siyavula platform. The goal is to understand user learning trajectories and identify potentially problematic sections that may require further attention or modification. This analysis is based on an anonymized dataset of user behavior data collected from the Siyavula platform.

## 2. Objective

The primary objectives of this analysis are to:

* Analyze practice data to understand user behavior on the Siyavula platform.
* Investigate typical trajectories of mastery vs. questions attempted across all sections.
* Identify potentially problematic sections in the learning process where users struggle or mastery doesn't improve.

## 3. Dataset

The dataset consists of 14 CSV files, each containing different aspects of user interaction data. The files are:

* **all\_attempts.csv:** Contains all attempts by learners meeting the inclusion criteria from the start of the year preceding the period under investigation (2022-01-01).
* **attempts.csv:** Contains identical fields to `all_attempts.csv` with additional fields such as `date_attempted`, `duration`, `nth_attempt_section`, `nth_attempt_overall`, and `nth_attempt_day`.
* **learner\_attempts\_agg.csv:** Contains attempts data aggregated to the learner level across all sections.
* **learner\_prompts\_agg.csv:** Contains prompts data aggregated to the learner level across all sections.
* **learner\_section\_attempts\_agg.csv:** Contains attempts data aggregated to the learner-section level.
* **learner\_section\_prompts\_agg.csv:** Contains prompts data aggregated to the learner-section level.
* **learner\_sectiontype\_attempts\_agg.csv:** Contains attempts data aggregated to the learner-sectiontype level.
* **learner\_sectiontype\_with\_masteries\_agg.csv:** Identical to `learner_sectiontype_attempts_agg.csv` with the addition of mastery aggregations.
* **learner\_with\_masteries\_agg.csv:** Identical to `learner_attempts_agg.csv` with the addition of mastery aggregations.
* **learners.csv:** Contains learner information such as `user_uuid`, `signed_up_at`, `group`, `province`, `quintile`, `master_school_id`, and `new_learner`.
* **prompts.csv:** Contains all prompts to revise associated with the learner's question attempts.
* **section\_mapping.csv:** Contains a mapping linking grade-level practice sections to the ID of the associated revision section.
* **sections.csv:** Contains all sections meeting inclusion criteria and their metadata.
* **struggling\_learner\_sections.csv:** Contains a list of learners who were sufficiently persistent in a given section.

A detailed description of each field in these CSV files can be found in the `README_DATA.md` file.

## 4. Steps

The analysis will involve the following steps:

1. **Initial Exploratory Data Analysis (EDA):**
    * Explore the dataset to understand its structure, fields, and potential issues with the data.
    * Visualize key aspects like user progress, mastery scores, and sections completed.
2. **Investigate Trajectories of Mastery vs. Questions Attempted:**
    * Analyze how users progress through various sections.
    * Plot trajectories for mastery vs. questions attempted for each section.
    * Identify patterns or anomalies in user progress.
3. **Identify Potentially Problematic Sections:**
    * Identify sections where users tend to get stuck or where mastery doesn’t improve.
    * Correlate these findings with section difficulty, user feedback, or other possible factors.

## 5. Deliverables

The primary deliverable is a Jupyter Notebook (Python 3.8) containing:

* Code for EDA, analysis, and visualizations.
* Detailed explanation and justification for any findings, particularly identifying problematic sections.
* Information on the libraries and versions used.

Additional deliverables include:

* A clear statement of assumptions made during the analysis.
* A well-documented and organized Jupyter Notebook that can be easily understood and reproduced by other team members.

## 6. Required Tools

* Python 3.8
* Jupyter Notebook
* Libraries for analysis and visualization (e.g., pandas, matplotlib, seaborn)

## 7. Conclusion

This analysis aims to provide insights into user practice behavior on the Siyavula platform. By understanding learning trajectories and identifying problematic sections, we can inform improvements to the platform and enhance learning outcomes for students.
