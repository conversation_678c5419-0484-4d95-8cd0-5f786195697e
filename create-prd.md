# Create PRD for Part 2 - Practice Patterns

## Overview

This document outlines the requirements for Part 2 of the Data Analyst Assignment - **Practice Patterns**.

---

## Objective

In this task, we aim to:

1. **Analyze Practice Data:** Review the anonymized dataset provided for users’ practice behavior.
2. **Investigate Learning Patterns:** Investigate typical trajectories of mastery vs questions attempted across all sections.
3. **Identify Problematic Sections:** Analyze the data to identify potentially problematic sections in the learning process.

---

## Dataset

The data used in this analysis comes from an anonymized set of user behavior data on Siyavula's practice platform. The dataset simulates a collection of linked database tables, providing insight into users' interactions with various sections of the practice platform.

---

## Steps

1. **Initial Exploratory Data Analysis (EDA):**
    - Explore the dataset to understand its structure, fields, and potential issues with the data.
    - Visualize key aspects like user progress, mastery scores, and sections completed.

2. **Investigate Trajectories of Mastery vs Questions Attempted:**
    - Analyze how users progress through various sections.
    - Plot trajectories for mastery vs questions attempted for each section.
    - Identify patterns or anomalies in user progress.

3. **Identify Potentially Problematic Sections:**
    - Identify sections where users tend to get stuck or where mastery doesn’t improve.
    - Correlate these findings with section difficulty, user feedback, or other possible factors.

---

## Deliverables

1. **Jupyter Notebook (Python 3.8):**
    - Code for EDA, analysis, and visualizations.
    - Detailed explanation and justification for any findings, particularly identifying problematic sections.
    - Notebook should include information on the libraries and versions used.

2. **Assumptions:**
    - The dataset may have missing values or other inconsistencies.
    - Trajectories and mastery levels should be assessed in the context of section difficulty.

3. **Submission:**
    - Send the saved Jupyter notebook, including all notes and output.

---

## Required Tools

- Python 3.8
- Jupyter Notebook
- Libraries for analysis and visualization (e.g., pandas, matplotlib, seaborn)

---

## Conclusion

The goal of Part 2 is to understand user practice behavior, particularly the learning trajectories across sections, and to identify which sections may need additional attention or modification to improve learning outcomes.
