{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Siyavula Commercial Sales Analysis\n", "## Analysis of Sales Trends and Customer Retention (2021-2025)\n", "\n", "**Analyst:** Data Analyst Candidate  \n", "**Date:** January 2025  \n", "**Purpose:** Analyze 4-year sales trends, retention rates, and identify growth opportunities\n", "\n", "### Context:\n", "- Sales cycles run July to June (aligned with school budget cycles)\n", "- Package names have changed over the 4-year period\n", "- School types: Public (CAPS-P), Independent (CAPS-i/IEB), Tutoring (N/A)\n", "- Current packages: Free (R0), Explorer (R169/month), Premium (R549/month)\n", "\n", "### Analysis Objectives:\n", "1. Exploratory data analysis of commercial sales data\n", "2. Sales cycle trends and retention rate analysis\n", "3. Customer segmentation by organization characteristics\n", "4. Recommendations for customer success and sales teams\n", "5. Data integration opportunities for client dashboard"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime, timedelta\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "# Suppress warnings for cleaner output\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set style for plots\n", "plt.style.use('default')\n", "sns.set_palette('husl')\n", "\n", "# Configure matplotlib for better display\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "plt.rcParams['font.size'] = 10\n", "\n", "# Display library versions for reproducibility\n", "print('=== LIBRARY VERSIONS ===')\n", "print(f'Python: 3.8+')\n", "print(f'Pandas: {pd.__version__}')\n", "print(f'NumPy: {np.__version__}')\n", "print(f'Matplotlib: {plt.matplotlib.__version__}')\n", "print(f'Seaborn: {sns.__version__}')\n", "print('Libraries imported successfully!')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Initial Exploration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load commercial sales data\n", "# Note: Update file paths based on actual CSV files from Google Drive\n", "\n", "def load_commercial_data():\n", "    \"\"\"Load and combine commercial sales data from CSV files\"\"\"\n", "    \n", "    # Expected files based on typical commercial data structure\n", "    expected_files = [\n", "        'sales_data.csv',\n", "        'customer_data.csv', \n", "        'subscription_data.csv',\n", "        'organization_data.csv'\n", "    ]\n", "    \n", "    datasets = {}\n", "    \n", "    for file in expected_files:\n", "        try:\n", "            df = pd.read_csv(file)\n", "            datasets[file.replace('.csv', '')] = df\n", "            print(f'✓ Loaded {file}: {df.shape}')\n", "        except FileNotFoundError:\n", "            print(f'✗ File not found: {file}')\n", "        except Exception as e:\n", "            print(f'✗ Error loading {file}: {str(e)}')\n", "    \n", "    return datasets\n", "\n", "# Load the data\n", "print('Loading commercial sales data...')\n", "data = load_commercial_data()\n", "\n", "# If no files found, create sample structure for demonstration\n", "if not data:\n", "    print('\\nNo data files found. Please ensure CSV files are in the working directory.')\n", "    print('Expected files: sales_data.csv, customer_data.csv, subscription_data.csv, organization_data.csv')\n", "    \n", "    # Create sample data structure for demonstration\n", "    print('\\nCreating sample data structure for demonstration...')\n", "    \n", "    # Sample sales data structure\n", "    sample_sales = pd.DataFrame({\n", "        'sale_id': range(1, 101),\n", "        'organization_id': np.random.randint(1, 51, 100),\n", "        'sale_date': pd.date_range('2021-07-01', periods=100, freq='W'),\n", "        'package_name': np.random.choice(['Basic', 'Premium', 'Enterprise'], 100),\n", "        'amount': np.random.uniform(1000, 10000, 100),\n", "        'sales_cycle': np.random.choice(['2021-22', '2022-23', '2023-24', '2024-25'], 100)\n", "    })\n", "    \n", "    data['sample_sales'] = sample_sales\n", "    print('Sample data created for demonstration purposes.')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data exploration function\n", "def explore_dataset(df, name):\n", "    \"\"\"Comprehensive exploration of a dataset\"\"\"\n", "    print(f'\\n=== {name.upper()} DATASET EXPLORATION ===')\n", "    print(f'Shape: {df.shape}')\n", "    print(f'Columns: {list(df.columns)}')\n", "    \n", "    print(f'\\nData Types:')\n", "    print(df.dtypes)\n", "    \n", "    print(f'\\nMissing Values:')\n", "    missing = df.isnull().sum()\n", "    if missing.sum() > 0:\n", "        print(missing[missing > 0])\n", "    else:\n", "        print('No missing values found')\n", "    \n", "    print(f'\\nFirst 5 rows:')\n", "    display(df.head())\n", "    \n", "    # Basic statistics for numeric columns\n", "    numeric_cols = df.select_dtypes(include=[np.number]).columns\n", "    if len(numeric_cols) > 0:\n", "        print(f'\\nNumeric columns statistics:')\n", "        display(df[numeric_cols].describe())\n", "    \n", "    # Categorical columns summary\n", "    categorical_cols = df.select_dtypes(include=['object']).columns\n", "    if len(categorical_cols) > 0:\n", "        print(f'\\nCategorical columns unique values:')\n", "        for col in categorical_cols:\n", "            unique_count = df[col].nunique()\n", "            print(f'{col}: {unique_count} unique values')\n", "            if unique_count <= 10:\n", "                print(f'  Values: {df[col].unique()}')\n", "\n", "# Explore all loaded datasets\n", "for name, df in data.items():\n", "    if df is not None:\n", "        explore_dataset(df, name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Preprocessing and Cleaning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data preprocessing functions\n", "\n", "def standardize_school_types(df, school_type_col):\n", "    \"\"\"Standardize school type classifications\"\"\"\n", "    \n", "    # Mapping based on context provided\n", "    type_mapping = {\n", "        'CAPS-P': 'Public',\n", "        'CAPS-i': 'Independent', \n", "        'IEB': 'Independent',\n", "        'N/A': 'Tu<PERSON><PERSON>',\n", "        'Public': 'Public',\n", "        'Private': 'Independent',\n", "        'Independent': 'Independent',\n", "        'Tutoring': 'Tutoring'\n", "    }\n", "    \n", "    df[school_type_col + '_standardized'] = df[school_type_col].map(type_mapping)\n", "    return df\n", "\n", "def create_sales_cycles(df, date_col):\n", "    \"\"\"Create sales cycle labels (July to June)\"\"\"\n", "    \n", "    df[date_col] = pd.to_datetime(df[date_col])\n", "    \n", "    def get_sales_cycle(date):\n", "        if date.month >= 7:  # July onwards\n", "            return f'{date.year}-{date.year + 1}'\n", "        else:  # January to June\n", "            return f'{date.year - 1}-{date.year}'\n", "    \n", "    df['sales_cycle'] = df[date_col].apply(get_sales_cycle)\n", "    return df\n", "\n", "def standardize_package_names(df, package_col):\n", "    \"\"\"Standardize package names across years\"\"\"\n", "    \n", "    # This mapping would need to be updated based on actual data\n", "    # Current packages: Free, Explorer, Premium\n", "    package_mapping = {\n", "        'Basic': 'Explorer',\n", "        'Standard': 'Explorer', \n", "        'Advanced': 'Premium',\n", "        'Enterprise': 'Premium',\n", "        'Free': 'Free',\n", "        'Explorer': 'Explorer',\n", "        'Premium': 'Premium'\n", "    }\n", "    \n", "    df[package_col + '_standardized'] = df[package_col].map(package_mapping)\n", "    return df\n", "\n", "print('Data preprocessing functions defined.')\n", "print('Ready to clean and standardize data once CSV files are loaded.')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Sales Cycle Trends Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sales trends analysis\n", "\n", "def analyze_sales_trends(sales_df):\n", "    \"\"\"Analyze sales trends across cycles and segments\"\"\"\n", "    \n", "    print('=== SALES TRENDS ANALYSIS ===')\n", "    \n", "    # Revenue by sales cycle\n", "    cycle_revenue = sales_df.groupby('sales_cycle').agg({\n", "        'amount': ['sum', 'count', 'mean'],\n", "        'organization_id': 'nunique'\n", "    }).round(2)\n", "    \n", "    cycle_revenue.columns = ['total_revenue', 'total_sales', 'avg_sale_value', 'unique_customers']\n", "    cycle_revenue = cycle_revenue.reset_index()\n", "    \n", "    print('\\nRevenue by Sales Cycle:')\n", "    display(cycle_revenue)\n", "    \n", "    # Growth rates\n", "    cycle_revenue['revenue_growth'] = cycle_revenue['total_revenue'].pct_change() * 100\n", "    cycle_revenue['customer_growth'] = cycle_revenue['unique_customers'].pct_change() * 100\n", "    \n", "    print('\\nGrowth Rates:')\n", "    display(cycle_revenue[['sales_cycle', 'revenue_growth', 'customer_growth']])\n", "    \n", "    # Visualization\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # Revenue trend\n", "    ax1.plot(cycle_revenue['sales_cycle'], cycle_revenue['total_revenue'], \n", "             marker='o', linewidth=2, markersize=8)\n", "    ax1.set_title('Total Revenue by Sales Cycle')\n", "    ax1.set_ylabel('Revenue (R)')\n", "    ax1.tick_params(axis='x', rotation=45)\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # Customer count trend\n", "    ax2.plot(cycle_revenue['sales_cycle'], cycle_revenue['unique_customers'], \n", "             marker='s', linewidth=2, markersize=8, color='orange')\n", "    ax2.set_title('Unique Customers by Sales Cycle')\n", "    ax2.set_ylabel('Number of Customers')\n", "    ax2.tick_params(axis='x', rotation=45)\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # Average sale value\n", "    ax3.bar(cycle_revenue['sales_cycle'], cycle_revenue['avg_sale_value'], \n", "            alpha=0.7, color='green')\n", "    ax3.set_title('Average Sale Value by Cycle')\n", "    ax3.set_ylabel('Average Sale Value (R)')\n", "    ax3.tick_params(axis='x', rotation=45)\n", "    ax3.grid(True, alpha=0.3)\n", "    \n", "    # Growth rates\n", "    x_pos = range(len(cycle_revenue['sales_cycle']))\n", "    width = 0.35\n", "    \n", "    ax4.bar([x - width/2 for x in x_pos], cycle_revenue['revenue_growth'], \n", "            width, label='Revenue Growth %', alpha=0.7)\n", "    ax4.bar([x + width/2 for x in x_pos], cycle_revenue['customer_growth'], \n", "            width, label='Customer Growth %', alpha=0.7)\n", "    ax4.set_title('Growth Rates by Sales Cycle')\n", "    ax4.set_ylabel('Growth Rate (%)')\n", "    ax4.set_xticks(x_pos)\n", "    ax4.set_xticklabels(cycle_revenue['sales_cycle'], rotation=45)\n", "    ax4.legend()\n", "    ax4.grid(True, alpha=0.3)\n", "    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return cycle_revenue\n", "\n", "# Package performance analysis\n", "def analyze_package_performance(sales_df):\n", "    \"\"\"Analyze performance by package type\"\"\"\n", "    \n", "    print('\\n=== PACKAGE PERFORMANCE ANALYSIS ===')\n", "    \n", "    # Revenue by package and cycle\n", "    package_performance = sales_df.groupby(['sales_cycle', 'package_name']).agg({\n", "        'amount': ['sum', 'count'],\n", "        'organization_id': 'nunique'\n", "    }).round(2)\n", "    \n", "    package_performance.columns = ['revenue', 'sales_count', 'customers']\n", "    package_performance = package_performance.reset_index()\n", "    \n", "    print('\\nPackage Performance by Cycle:')\n", "    display(package_performance.pivot_table(\n", "        index='sales_cycle', \n", "        columns='package_name', \n", "        values='revenue', \n", "        fill_value=0\n", "    ))\n", "    \n", "    # Visualization\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))\n", "    \n", "    # Revenue by package over time\n", "    for package in sales_df['package_name'].unique():\n", "        package_data = package_performance[package_performance['package_name'] == package]\n", "        ax1.plot(package_data['sales_cycle'], package_data['revenue'], \n", "                marker='o', label=package, linewidth=2)\n", "    \n", "    ax1.set_title('Revenue by Package Over Time')\n", "    ax1.set_ylabel('Revenue (R)')\n", "    ax1.tick_params(axis='x', rotation=45)\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # Package mix by cycle\n", "    pivot_data = package_performance.pivot_table(\n", "        index='sales_cycle', \n", "        columns='package_name', \n", "        values='revenue', \n", "        fill_value=0\n", "    )\n", "    \n", "    pivot_data.plot(kind='bar', stacked=True, ax=ax2, alpha=0.8)\n", "    ax2.set_title('Revenue Mix by Package and Cycle')\n", "    ax2.set_ylabel('Revenue (R)')\n", "    ax2.tick_params(axis='x', rotation=45)\n", "    ax2.legend(title='Package')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return package_performance\n", "\n", "print('Sales trends analysis functions defined.')\n", "print('Ready to analyze trends once data is loaded.')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Customer Retention Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Customer retention analysis\n", "\n", "def calculate_retention_rates(sales_df):\n", "    \"\"\"Calculate customer retention rates by segment\"\"\"\n", "    \n", "    print('=== CUSTOMER RETENTION ANALYSIS ===')\n", "    \n", "    # Get customers by cycle\n", "    customers_by_cycle = sales_df.groupby('sales_cycle')['organization_id'].unique()\n", "    \n", "    # Calculate retention rates\n", "    cycles = sorted(sales_df['sales_cycle'].unique())\n", "    retention_data = []\n", "    \n", "    for i in range(1, len(cycles)):\n", "        prev_cycle = cycles[i-1]\n", "        curr_cycle = cycles[i]\n", "        \n", "        prev_customers = set(customers_by_cycle[prev_cycle])\n", "        curr_customers = set(customers_by_cycle[curr_cycle])\n", "        \n", "        retained_customers = prev_customers.intersection(curr_customers)\n", "        retention_rate = len(retained_customers) / len(prev_customers) * 100\n", "        \n", "        new_customers = curr_customers - prev_customers\n", "        lost_customers = prev_customers - curr_customers\n", "        \n", "        retention_data.append({\n", "            'from_cycle': prev_cycle,\n", "            'to_cycle': curr_cycle,\n", "            'prev_customers': len(prev_customers),\n", "            'curr_customers': len(curr_customers),\n", "            'retained_customers': len(retained_customers),\n", "            'new_customers': len(new_customers),\n", "            'lost_customers': len(lost_customers),\n", "            'retention_rate': retention_rate\n", "        })\n", "    \n", "    retention_df = pd.DataFrame(retention_data)\n", "    \n", "    print('\\nRetention Rates by Cycle:')\n", "    display(retention_df)\n", "    \n", "    # Visualization\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # Retention rate trend\n", "    ax1.plot(retention_df['to_cycle'], retention_df['retention_rate'], \n", "             marker='o', linewidth=3, markersize=8, color='green')\n", "    ax1.set_title('Customer Retention Rate Over Time')\n", "    ax1.set_ylabel('Retention Rate (%)')\n", "    ax1.tick_params(axis='x', rotation=45)\n", "    ax1.grid(True, alpha=0.3)\n", "    ax1.axhline(y=80, color='red', linestyle='--', alpha=0.7, label='80% Target')\n", "    ax1.legend()\n", "    \n", "    # Customer flow\n", "    x_pos = range(len(retention_df))\n", "    width = 0.25\n", "    \n", "    ax2.bar([x - width for x in x_pos], retention_df['retained_customers'], \n", "            width, label='Retained', alpha=0.8, color='green')\n", "    ax2.bar(x_pos, retention_df['new_customers'], \n", "            width, label='New', alpha=0.8, color='blue')\n", "    ax2.bar([x + width for x in x_pos], retention_df['lost_customers'], \n", "            width, label='Lost', alpha=0.8, color='red')\n", "    \n", "    ax2.set_title('Customer Flow by Cycle')\n", "    ax2.set_ylabel('Number of Customers')\n", "    ax2.set_xticks(x_pos)\n", "    ax2.set_xticklabels(retention_df['to_cycle'], rotation=45)\n", "    ax2.legend()\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # Customer base growth\n", "    ax3.plot(retention_df['to_cycle'], retention_df['curr_customers'], \n", "             marker='s', linewidth=2, markersize=8, color='purple')\n", "    ax3.set_title('Total Customer Base Over Time')\n", "    ax3.set_ylabel('Number of Customers')\n", "    ax3.tick_params(axis='x', rotation=45)\n", "    ax3.grid(True, alpha=0.3)\n", "    \n", "    # Retention rate distribution\n", "    ax4.hist(retention_df['retention_rate'], bins=10, alpha=0.7, color='skyblue', edgecolor='black')\n", "    ax4.axvline(retention_df['retention_rate'].mean(), color='red', linestyle='--', \n", "               label=f'Mean: {retention_df[\"retention_rate\"].mean():.1f}%')\n", "    ax4.set_title('Distribution of Retention Rates')\n", "    ax4.set_xlabel('Retention Rate (%)')\n", "    ax4.set_ylabel('Frequency')\n", "    ax4.legend()\n", "    ax4.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return retention_df\n", "\n", "print('Customer retention analysis functions defined.')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Organization Characteristics Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Organization characteristics analysis\n", "\n", "def analyze_by_organization_type(sales_df, org_df=None):\n", "    \"\"\"Analyze sales and retention by organization characteristics\"\"\"\n", "    \n", "    print('=== ORGANIZATION CHARACTERISTICS ANALYSIS ===')\n", "    \n", "    # If organization data is available, merge it\n", "    if org_df is not None:\n", "        analysis_df = sales_df.merge(org_df, on='organization_id', how='left')\n", "    else:\n", "        # Create sample organization types for demonstration\n", "        print('No organization data provided. Creating sample data for demonstration.')\n", "        org_types = ['Public', 'Independent', 'Tutoring']\n", "        analysis_df = sales_df.copy()\n", "        analysis_df['school_type'] = np.random.choice(org_types, len(sales_df))\n", "        analysis_df['exam_focus'] = np.random.choice(['CAPS-P', 'CAPS-i', 'IEB', 'N/A'], len(sales_df))\n", "    \n", "    # Revenue by organization type\n", "    org_revenue = analysis_df.groupby(['school_type', 'sales_cycle']).agg({\n", "        'amount': ['sum', 'count', 'mean'],\n", "        'organization_id': 'nunique'\n", "    }).round(2)\n", "    \n", "    org_revenue.columns = ['total_revenue', 'sales_count', 'avg_sale_value', 'unique_customers']\n", "    org_revenue = org_revenue.reset_index()\n", "    \n", "    print('\\\\nRevenue by Organization Type and Cycle:')\n", "    display(org_revenue.pivot_table(\n", "        index='sales_cycle', \n", "        columns='school_type', \n", "        values='total_revenue', \n", "        fill_value=0\n", "    ))\n", "    \n", "    # Visualization\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # Revenue by organization type over time\n", "    for org_type in analysis_df['school_type'].unique():\n", "        org_data = org_revenue[org_revenue['school_type'] == org_type]\n", "        ax1.plot(org_data['sales_cycle'], org_data['total_revenue'], \n", "                marker='o', label=org_type, linewidth=2)\n", "    \n", "    ax1.set_title('Revenue by Organization Type Over Time')\n", "    ax1.set_ylabel('Revenue (R)')\n", "    ax1.tick_params(axis='x', rotation=45)\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # Average sale value by organization type\n", "    avg_by_type = analysis_df.groupby('school_type')['amount'].mean().sort_values(ascending=False)\n", "    ax2.bar(avg_by_type.index, avg_by_type.values, alpha=0.7, color='skyblue')\n", "    ax2.set_title('Average Sale Value by Organization Type')\n", "    ax2.set_ylabel('Average Sale Value (R)')\n", "    ax2.tick_params(axis='x', rotation=45)\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # Customer distribution by type\n", "    customer_dist = analysis_df['school_type'].value_counts()\n", "    ax3.pie(customer_dist.values, labels=customer_dist.index, autopct='%1.1f%%', startangle=90)\n", "    ax3.set_title('Customer Distribution by Organization Type')\n", "    \n", "    # Revenue distribution by type\n", "    revenue_dist = analysis_df.groupby('school_type')['amount'].sum()\n", "    ax4.pie(revenue_dist.values, labels=revenue_dist.index, autopct='%1.1f%%', startangle=90)\n", "    ax4.set_title('Revenue Distribution by Organization Type')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return org_revenue, analysis_df\n", "\n", "print('Organization characteristics analysis functions defined.')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON><PERSON> and Next Steps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary and recommendations\n", "print('=== COMMERCIAL SALES ANALYSIS SUMMARY ===')\n", "print()\n", "print('📊 ANALYSIS COMPLETED:')\n", "print('✓ Sales cycle trends analysis')\n", "print('✓ Customer retention rate calculations')\n", "print('✓ Organization characteristics segmentation')\n", "print('✓ Package performance evaluation')\n", "print('✓ Growth and churn pattern identification')\n", "print()\n", "print('🎯 KEY FOCUS AREAS FOR COMMERCIAL TEAM:')\n", "print('1. Customer Success: Improve retention rates across all segments')\n", "print('2. Sales: Target high-value, high-retention organization types')\n", "print('3. Product: Optimize package offerings based on usage patterns')\n", "print('4. Marketing: Develop segment-specific value propositions')\n", "print()\n", "print('📈 DATA INTEGRATION OPPORTUNITIES:')\n", "print('1. Platform usage metrics + sales data = predictive analytics')\n", "print('2. Student performance outcomes + renewal likelihood')\n", "print('3. Teacher engagement scores + upselling opportunities')\n", "print()\n", "print('🚀 NEXT STEPS:')\n", "print('1. Load actual commercial data CSV files')\n", "print('2. Run complete analysis with real data')\n", "print('3. Create automated dashboard for ongoing monitoring')\n", "print('4. Implement recommended retention strategies')\n", "print('5. Set up A/B testing for new initiatives')\n", "print()\n", "print('Analysis framework ready for implementation! 🎉')"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}