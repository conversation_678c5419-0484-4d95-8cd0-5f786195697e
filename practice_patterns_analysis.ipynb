import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import warnings
from pathlib import Path

warnings.filterwarnings('ignore')

plt.style.use('default')
sns.set_palette('husl')

plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

print('Libraries imported successfully!')
print(f'Pandas version: {pd.__version__}')
print(f'Matplotlib version: {plt.matplotlib.__version__}')
print(f'Seaborn version: {sns.__version__}')

datasets = {}
csv_files = [
    'all_attempts.csv',
    'attempts.csv', 
    'learner_attempts_agg.csv',
    'learner_prompts_agg.csv',
    'learner_section_attempts_agg.csv',
    'learner_section_prompts_agg.csv',
    'learner_sectiontype_attempts_agg.csv',
    'learner_sectiontype_with_masteries_agg.csv',
    'learner_with_masteries_agg.csv',
    'learners.csv',
    'prompts.csv',
    'section_mapping.csv',
    'sections.csv',
    'struggling_learner_sections.csv'
]

for file in csv_files:
    try:
        dataset_name = file.replace('.csv', '')
        datasets[dataset_name] = pd.read_csv(file)
        print(f'Loaded {file}: {datasets[dataset_name].shape}')
    except FileNotFoundError:
        print(f'File not found: {file}')
    except Exception as e:
        print(f'Error loading {file}: {str(e)}')

all_attempts = datasets.get('all_attempts')
attempts = datasets.get('attempts')
learner_attempts_agg = datasets.get('learner_attempts_agg')
learner_prompts_agg = datasets.get('learner_prompts_agg')
learner_section_attempts_agg = datasets.get('learner_section_attempts_agg')
learner_section_prompts_agg = datasets.get('learner_section_prompts_agg')
learner_sectiontype_attempts_agg = datasets.get('learner_sectiontype_attempts_agg')
learner_sectiontype_with_masteries_agg = datasets.get('learner_sectiontype_with_masteries_agg')
learner_with_masteries_agg = datasets.get('learner_with_masteries_agg')
learners = datasets.get('learners')
prompts = datasets.get('prompts')
section_mapping = datasets.get('section_mapping')
sections = datasets.get('sections')
struggling_learner_sections = datasets.get('struggling_learner_sections')

print(f'\nTotal datasets loaded: {len([d for d in datasets.values() if d is not None])}')

def explore_dataset(df, name):
    """Explore basic statistics and structure of a dataset"""
    if df is None:
        print(f'Dataset {name} is not available')
        return
    
    print(f'\n=== {name.upper()} DATASET ===')
    print(f'Shape: {df.shape}')
    print(f'Columns: {list(df.columns)}')
    print(f'Data types:\n{df.dtypes}')
    print(f'\nMissing values:\n{df.isnull().sum()}')
    print(f'\nFirst few rows:')
    display(df.head())
 
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    if len(numeric_cols) > 0:
        print(f'\nBasic statistics for numeric columns:')
        display(df[numeric_cols].describe())

key_datasets = {
    'learner_section_attempts_agg': learner_section_attempts_agg,
    'sections': sections,
    'learners': learners,
    'struggling_learner_sections': struggling_learner_sections
}

for name, df in key_datasets.items():
    explore_dataset(df, name)

if learner_section_attempts_agg is not None and sections is not None:
    
    required_cols = ['section_id', 'attempts', 'mastery']
    if all(col in learner_section_attempts_agg.columns for col in required_cols):
        
        # 1. Individual section analysis
        print('=== INDIVIDUAL SECTION ANALYSIS ===')
        
        # Get a sample section for detailed analysis
        if 'section_id' in sections.columns and len(sections) > 0:
            sample_section_id = sections['section_id'].iloc[0]
            section_data = learner_section_attempts_agg[
                learner_section_attempts_agg['section_id'] == sample_section_id
            ]
            
            if len(section_data) > 0:
                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
                
                # Scatter plot
                ax1.scatter(section_data['attempts'], section_data['mastery'], 
                           alpha=0.6, s=50, color='steelblue')
                ax1.set_title(f'Mastery vs. Attempts\nSection {sample_section_id}')
                ax1.set_xlabel('Number of Attempts')
                ax1.set_ylabel('Mastery Score')
                ax1.grid(True, alpha=0.3)
                
                # Add trend line
                z = np.polyfit(section_data['attempts'], section_data['mastery'], 1)
                p = np.poly1d(z)
                ax1.plot(section_data['attempts'], p(section_data['attempts']), 
                        'r--', alpha=0.8, label=f'Trend line')
                ax1.legend()
                
                # Distribution of attempts
                ax2.hist(section_data['attempts'], bins=20, alpha=0.7, color='lightcoral')
                ax2.set_title(f'Distribution of Attempts\nSection {sample_section_id}')
                ax2.set_xlabel('Number of Attempts')
                ax2.set_ylabel('Frequency')
                ax2.grid(True, alpha=0.3)
                
                plt.tight_layout()
                plt.show()
                
                print(f'Section {sample_section_id} Statistics:')
                print(f'- Total learners: {len(section_data)}')
                print(f'- Average attempts: {section_data["attempts"].mean():.2f}')
                print(f'- Average mastery: {section_data["mastery"].mean():.2f}')
                print(f'- Correlation (attempts vs mastery): {section_data["attempts"].corr(section_data["mastery"]):.3f}')
        
        # 2. Aggregate analysis across all sections
        print('\n=== AGGREGATE ANALYSIS ACROSS ALL SECTIONS ===')
        
        aggregated_data = learner_section_attempts_agg.groupby('section_id').agg({
            'attempts': ['mean', 'std', 'count'],
            'mastery': ['mean', 'std']
        }).round(3)
        
        # Flatten column names
        aggregated_data.columns = ['_'.join(col).strip() for col in aggregated_data.columns]
        aggregated_data = aggregated_data.reset_index()
        
        # Create comprehensive visualization
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. Average mastery vs average attempts
        scatter = ax1.scatter(aggregated_data['attempts_mean'], 
                            aggregated_data['mastery_mean'],
                            s=aggregated_data['attempts_count']*2,
                            alpha=0.6, c=aggregated_data['mastery_mean'], 
                            cmap='RdYlBu_r')
        ax1.set_title('Average Mastery vs. Average Attempts\n(Size = Number of Learners)')
        ax1.set_xlabel('Average Number of Attempts')
        ax1.set_ylabel('Average Mastery Score')
        ax1.grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=ax1, label='Mastery Score')
        
        # 2. Distribution of average attempts
        ax2.hist(aggregated_data['attempts_mean'], bins=20, alpha=0.7, color='skyblue')
        ax2.set_title('Distribution of Average Attempts per Section')
        ax2.set_xlabel('Average Attempts')
        ax2.set_ylabel('Number of Sections')
        ax2.grid(True, alpha=0.3)
        
        # 3. Distribution of average mastery
        ax3.hist(aggregated_data['mastery_mean'], bins=20, alpha=0.7, color='lightgreen')
        ax3.set_title('Distribution of Average Mastery per Section')
        ax3.set_xlabel('Average Mastery Score')
        ax3.set_ylabel('Number of Sections')
        ax3.grid(True, alpha=0.3)
        
        # 4. Attempts vs Mastery correlation by section
        section_correlations = []
        for section_id in aggregated_data['section_id']:
            section_subset = learner_section_attempts_agg[
                learner_section_attempts_agg['section_id'] == section_id
            ]
            if len(section_subset) > 1:
                corr = section_subset['attempts'].corr(section_subset['mastery'])
                section_correlations.append(corr if not np.isnan(corr) else 0)
            else:
                section_correlations.append(0)
        
        ax4.hist(section_correlations, bins=20, alpha=0.7, color='orange')
        ax4.set_title('Distribution of Attempts-Mastery Correlations\nAcross Sections')
        ax4.set_xlabel('Correlation Coefficient')
        ax4.set_ylabel('Number of Sections')
        ax4.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='Zero correlation')
        ax4.grid(True, alpha=0.3)
        ax4.legend()
        
        plt.tight_layout()
        plt.show()
        
        # 3. Identify problematic sections
        print('\n=== PROBLEMATIC SECTIONS IDENTIFICATION ===')
        
        high_attempts_threshold = aggregated_data['attempts_mean'].quantile(0.75)
        low_mastery_threshold = aggregated_data['mastery_mean'].quantile(0.25)
        
        problematic_sections = aggregated_data[
            (aggregated_data['attempts_mean'] > high_attempts_threshold) & 
            (aggregated_data['mastery_mean'] < low_mastery_threshold)
        ]
        
        print(f'Thresholds used:')
        print(f'- High attempts threshold (75th percentile): {high_attempts_threshold:.2f}')
        print(f'- Low mastery threshold (25th percentile): {low_mastery_threshold:.2f}')
        print(f'\nProblematic sections found: {len(problematic_sections)}')
        
        if len(problematic_sections) > 0:
            display(problematic_sections[['section_id', 'attempts_mean', 'mastery_mean', 'attempts_count']])
        else:
            print('No sections meet the problematic criteria.')
            
    else:
        print(f'Required columns not found. Available columns: {list(learner_section_attempts_agg.columns)}')
else:
    print('Required datasets not available for analysis.')

# Identify sections where users tend to get stuck or where mastery doesn’t improve
# Correlate these findings with section difficulty, user feedback, or other possible factors

# Example: Identifying sections with low mastery scores after multiple attempts
threshold_attempts = 5  # Define a threshold for the number of attempts
low_mastery_threshold = 0.5  # Define a threshold for low mastery

problematic_sections = learner_section_attempts_agg[
    (learner_section_attempts_agg['attempts'] > threshold_attempts) & 
    (learner_section_attempts_agg['mastery'] < low_mastery_threshold)
]

print("Problematic Sections (Individual Data):")
print(problematic_sections[['section_id', 'attempts', 'mastery']].head())

# Further analysis: Identify sections with a significant difference between attempts and mastery
learner_section_attempts_agg['attempts_mastery_diff'] = learner_section_attempts_agg['attempts'] - learner_section_attempts_agg['mastery']

significant_diff_threshold = learner_section_attempts_agg['attempts_mastery_diff'].quantile(0.9)  # 90th percentile

problematic_sections_diff = learner_section_attempts_agg[
    learner_section_attempts_agg['attempts_mastery_diff'] > significant_diff_threshold
]

print("Problematic Sections (Significant Difference between Attempts and Mastery):")
print(problematic_sections_diff[['section_id', 'attempts', 'mastery', 'attempts_mastery_diff']].head())

# Enhanced analysis with better error handling and insights
if learner_section_attempts_agg is not None:
    print('=== ENHANCED ANALYSIS AND INSIGHTS ===')
    
    # Check data availability
    required_cols = ['section_id', 'attempts', 'mastery']
    if all(col in learner_section_attempts_agg.columns for col in required_cols):
        
        # Calculate learning efficiency metrics
        df_analysis = learner_section_attempts_agg.copy()
        df_analysis['efficiency'] = df_analysis['mastery'] / df_analysis['attempts']
        df_analysis['efficiency'] = df_analysis['efficiency'].replace([np.inf, -np.inf], np.nan)
        
        # Section-level insights
        section_insights = df_analysis.groupby('section_id').agg({
            'attempts': ['count', 'mean', 'median', 'std'],
            'mastery': ['mean', 'median', 'std'],
            'efficiency': ['mean', 'median']
        }).round(3)
        
        section_insights.columns = ['learners', 'avg_attempts', 'med_attempts', 'std_attempts',
                                   'avg_mastery', 'med_mastery', 'std_mastery', 
                                   'avg_efficiency', 'med_efficiency']
        section_insights = section_insights.reset_index()
        
        # Identify different types of problematic sections
        high_effort_low_mastery = section_insights[
            (section_insights['avg_attempts'] > section_insights['avg_attempts'].quantile(0.75)) &
            (section_insights['avg_mastery'] < section_insights['avg_mastery'].quantile(0.25))
        ]
        
        low_efficiency = section_insights[
            section_insights['avg_efficiency'] < section_insights['avg_efficiency'].quantile(0.1)
        ]
        
        high_variability = section_insights[
            (section_insights['std_attempts'] > section_insights['std_attempts'].quantile(0.9)) |
            (section_insights['std_mastery'] > section_insights['std_mastery'].quantile(0.9))
        ]
        
        # Display insights
        print(f'\nSECTION CATEGORIES:')
        print(f'High Effort, Low Mastery sections: {len(high_effort_low_mastery)}')
        print(f'Low Efficiency sections: {len(low_efficiency)}')
        print(f'High Variability sections: {len(high_variability)}')
        
        if len(high_effort_low_mastery) > 0:
            print('\nHigh Effort, Low Mastery Sections:')
            display(high_effort_low_mastery[['section_id', 'learners', 'avg_attempts', 'avg_mastery', 'avg_efficiency']].head())
        
        # Create comprehensive visualization
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. Efficiency distribution
        valid_efficiency = df_analysis['efficiency'].dropna()
        ax1.hist(valid_efficiency, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.axvline(valid_efficiency.mean(), color='red', linestyle='--', 
                   label=f'Mean: {valid_efficiency.mean():.3f}')
        ax1.axvline(valid_efficiency.median(), color='orange', linestyle='--', 
                   label=f'Median: {valid_efficiency.median():.3f}')
        ax1.set_xlabel('Learning Efficiency (Mastery/Attempts)')
        ax1.set_ylabel('Frequency')
        ax1.set_title('Distribution of Learning Efficiency')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. Section performance scatter
        scatter = ax2.scatter(section_insights['avg_attempts'], section_insights['avg_mastery'],
                            s=section_insights['learners']*3, alpha=0.6,
                            c=section_insights['avg_efficiency'], cmap='RdYlGn')
        ax2.set_xlabel('Average Attempts')
        ax2.set_ylabel('Average Mastery')
        ax2.set_title('Section Performance Overview\n(Size=Learners, Color=Efficiency)')
        ax2.grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=ax2, label='Avg Efficiency')
        
        # 3. Attempts vs Mastery relationship
        ax3.scatter(df_analysis['attempts'], df_analysis['mastery'], alpha=0.4, s=20)
        
        # Add trend line
        z = np.polyfit(df_analysis['attempts'], df_analysis['mastery'], 1)
        p = np.poly1d(z)
        ax3.plot(df_analysis['attempts'], p(df_analysis['attempts']), 'r--', alpha=0.8)
        
        correlation = df_analysis['attempts'].corr(df_analysis['mastery'])
        ax3.set_xlabel('Attempts')
        ax3.set_ylabel('Mastery')
        ax3.set_title(f'Attempts vs Mastery Relationship\n(Correlation: {correlation:.3f})')
        ax3.grid(True, alpha=0.3)
        
        # 4. Box plot of mastery by attempt ranges
        df_analysis['attempt_range'] = pd.cut(df_analysis['attempts'], 
                                            bins=[0, 2, 5, 10, float('inf')],
                                            labels=['1-2', '3-5', '6-10', '10+'])
        
        sns.boxplot(data=df_analysis, x='attempt_range', y='mastery', ax=ax4)
        ax4.set_xlabel('Attempt Range')
        ax4.set_ylabel('Mastery Score')
        ax4.set_title('Mastery Distribution by Attempt Range')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
        # Summary insights
        print('\nKEY INSIGHTS:')
        print(f'• Overall correlation between attempts and mastery: {correlation:.3f}')
        print(f'• Average learning efficiency: {valid_efficiency.mean():.3f}')
        print(f'• {len(df_analysis[df_analysis["attempts"] > 10])} learners required >10 attempts')
        print(f'• {len(df_analysis[df_analysis["mastery"] < 0.3])} learners achieved <30% mastery')
        
        # Recommendations
        print('\nRECOMMENDATIONS:')
        if correlation < 0:
            print('• Negative correlation suggests more attempts may not lead to better mastery')
            print('• Consider reviewing instructional design and feedback mechanisms')
        elif correlation < 0.3:
            print('• Weak positive correlation suggests room for improvement in learning efficiency')
            print('• Consider adaptive learning paths or personalized interventions')
        
        if len(high_effort_low_mastery) > 0:
            print(f'• {len(high_effort_low_mastery)} sections show high effort but low mastery - priority for review')
        
        if len(high_variability) > 0:
            print(f'• {len(high_variability)} sections show high variability - may need differentiated instruction')
            
    else:
        print(f'Required columns missing. Available: {list(learner_section_attempts_agg.columns)}')
else:
    print('Dataset not available for enhanced analysis.')