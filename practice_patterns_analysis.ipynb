# Import libraries
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Set style for plots
sns.set_style('whitegrid')

# Load the datasets
all_attempts = pd.read_csv('all_attempts.csv')
attempts = pd.read_csv('attempts.csv')
learner_attempts_agg = pd.read_csv('learner_attempts_agg.csv')
learner_prompts_agg = pd.read_csv('learner_prompts_agg.csv')
learner_section_attempts_agg = pd.read_csv('learner_section_attempts_agg.csv')
learner_section_prompts_agg = pd.read_csv('learner_section_prompts_agg.csv')
learner_sectiontype_attempts_agg = pd.read_csv('learner_sectiontype_attempts_agg.csv')
learner_sectiontype_with_masteries_agg = pd.read_csv('learner_sectiontype_with_masteries_agg.csv')
learner_with_masteries_agg = pd.read_csv('learner_with_masteries_agg.csv')
learners = pd.read_csv('learners.csv')
prompts = pd.read_csv('prompts.csv')
section_mapping = pd.read_csv('section_mapping.csv')
sections = pd.read_csv('sections.csv')
struggling_learner_sections = pd.read_csv('struggling_learner_sections.csv')

# Analyze how users progress through various sections
# Plot trajectories for mastery vs. questions attempted for each section
# Identify patterns or anomalies in user progress

# Example: Plotting mastery vs. questions attempted for a specific section
section_id = sections['section_id'].iloc[0] # Select the first section_id
section_data = learner_section_attempts_agg[learner_section_attempts_agg['section_id'] == section_id]

plt.figure(figsize=(10, 6))
plt.scatter(section_data['attempts'], section_data['mastery'], alpha=0.5)
plt.title(f'Mastery vs. Attempts for Section {section_id}')
plt.xlabel('Number of Attempts')
plt.ylabel('Mastery Score')
plt.show()

# Further analysis: Aggregate data to plot average mastery vs. attempts for all sections
aggregated_data = learner_section_attempts_agg.groupby('section_id').agg({'attempts': 'mean', 'mastery': 'mean'}).reset_index()

plt.figure(figsize=(12, 8))
plt.scatter(aggregated_data['attempts'], aggregated_data['mastery'], alpha=0.5)
plt.title('Average Mastery vs. Average Attempts for All Sections')
plt.xlabel('Average Number of Attempts')
plt.ylabel('Average Mastery Score')
plt.show()

# Identify sections with a high number of attempts but low average mastery
high_attempts_threshold = aggregated_data['attempts'].quantile(0.75)  # 75th percentile
low_mastery_threshold = 0.5

problematic_sections_aggregated = aggregated_data[
    (aggregated_data['attempts'] > high_attempts_threshold) & 
    (aggregated_data['mastery'] < low_mastery_threshold)
]

print("Problematic Sections (Aggregated Data):")
print(problematic_sections_aggregated)


# Identify sections where users tend to get stuck or where mastery doesn’t improve
# Correlate these findings with section difficulty, user feedback, or other possible factors

# Example: Identifying sections with low mastery scores after multiple attempts
threshold_attempts = 5  # Define a threshold for the number of attempts
low_mastery_threshold = 0.5  # Define a threshold for low mastery

problematic_sections = learner_section_attempts_agg[
    (learner_section_attempts_agg['attempts'] > threshold_attempts) & 
    (learner_section_attempts_agg['mastery'] < low_mastery_threshold)
]

print("Problematic Sections (Individual Data):")
print(problematic_sections[['section_id', 'attempts', 'mastery']].head())

# Further analysis: Identify sections with a significant difference between attempts and mastery
learner_section_attempts_agg['attempts_mastery_diff'] = learner_section_attempts_agg['attempts'] - learner_section_attempts_agg['mastery']

significant_diff_threshold = learner_section_attempts_agg['attempts_mastery_diff'].quantile(0.9)  # 90th percentile

problematic_sections_diff = learner_section_attempts_agg[
    learner_section_attempts_agg['attempts_mastery_diff'] > significant_diff_threshold
]

print("Problematic Sections (Significant Difference between Attempts and Mastery):")
print(problematic_sections_diff[['section_id', 'attempts', 'mastery', 'attempts_mastery_diff']].head())