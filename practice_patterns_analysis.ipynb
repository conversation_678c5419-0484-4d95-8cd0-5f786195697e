{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named '<PERSON><PERSON><PERSON><PERSON><PERSON>'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36m<PERSON><PERSON>\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m \u001b[34;01mp<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[34;01mpd\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m \u001b[34;01mmat<PERSON>lotlib\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpyplot\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[34;01mplt\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m \u001b[34;01<PERSON><PERSON>born\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[34;01msns\u001b[39;00m\n\u001b[32m      4\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[34;01mnumpy\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[34;01mnp\u001b[39;00m\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'matplotlib'"]}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import numpy as np\n", "import warnings\n", "from pathlib import Path\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "plt.style.use('default')\n", "sns.set_palette('husl')\n", "\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "plt.rcParams['font.size'] = 10\n", "\n", "print('Libraries imported successfully!')\n", "print(f'Pandas version: {pd.__version__}')\n", "print(f'Matplotlib version: {plt.matplotlib.__version__}')\n", "print(f'Seaborn version: {sns.__version__}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Datasets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["datasets = {}\n", "csv_files = [\n", "    'all_attempts.csv',\n", "    'attempts.csv', \n", "    'learner_attempts_agg.csv',\n", "    'learner_prompts_agg.csv',\n", "    'learner_section_attempts_agg.csv',\n", "    'learner_section_prompts_agg.csv',\n", "    'learner_sectiontype_attempts_agg.csv',\n", "    'learner_sectiontype_with_masteries_agg.csv',\n", "    'learner_with_masteries_agg.csv',\n", "    'learners.csv',\n", "    'prompts.csv',\n", "    'section_mapping.csv',\n", "    'sections.csv',\n", "    'struggling_learner_sections.csv'\n", "]\n", "\n", "for file in csv_files:\n", "    try:\n", "        dataset_name = file.replace('.csv', '')\n", "        datasets[dataset_name] = pd.read_csv(file)\n", "        print(f'Loaded {file}: {datasets[dataset_name].shape}')\n", "    except FileNotFoundError:\n", "        print(f'File not found: {file}')\n", "    except Exception as e:\n", "        print(f'Error loading {file}: {str(e)}')\n", "\n", "all_attempts = datasets.get('all_attempts')\n", "attempts = datasets.get('attempts')\n", "learner_attempts_agg = datasets.get('learner_attempts_agg')\n", "learner_prompts_agg = datasets.get('learner_prompts_agg')\n", "learner_section_attempts_agg = datasets.get('learner_section_attempts_agg')\n", "learner_section_prompts_agg = datasets.get('learner_section_prompts_agg')\n", "learner_sectiontype_attempts_agg = datasets.get('learner_sectiontype_attempts_agg')\n", "learner_sectiontype_with_masteries_agg = datasets.get('learner_sectiontype_with_masteries_agg')\n", "learner_with_masteries_agg = datasets.get('learner_with_masteries_agg')\n", "learners = datasets.get('learners')\n", "prompts = datasets.get('prompts')\n", "section_mapping = datasets.get('section_mapping')\n", "sections = datasets.get('sections')\n", "struggling_learner_sections = datasets.get('struggling_learner_sections')\n", "\n", "print(f'\\nTotal datasets loaded: {len([d for d in datasets.values() if d is not None])}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Exploration and Overview"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def explore_dataset(df, name):\n", "    \"\"\"Explore basic statistics and structure of a dataset\"\"\"\n", "    if df is None:\n", "        print(f'Dataset {name} is not available')\n", "        return\n", "    \n", "    print(f'\\n=== {name.upper()} DATASET ===')\n", "    print(f'Shape: {df.shape}')\n", "    print(f'Columns: {list(df.columns)}')\n", "    print(f'Data types:\\n{df.dtypes}')\n", "    print(f'\\nMissing values:\\n{df.isnull().sum()}')\n", "    print(f'\\nFirst few rows:')\n", "    display(df.head())\n", " \n", "    numeric_cols = df.select_dtypes(include=[np.number]).columns\n", "    if len(numeric_cols) > 0:\n", "        print(f'\\nBasic statistics for numeric columns:')\n", "        display(df[numeric_cols].describe())\n", "\n", "key_datasets = {\n", "    'learner_section_attempts_agg': learner_section_attempts_agg,\n", "    'sections': sections,\n", "    'learners': learners,\n", "    'struggling_learner_sections': struggling_learner_sections\n", "}\n", "\n", "for name, df in key_datasets.items():\n", "    explore_dataset(df, name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Investigate Trajectories of Mastery vs. Questions Attempted"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if learner_section_attempts_agg is not None and sections is not None:\n", "    \n", "    required_cols = ['section_id', 'attempts', 'mastery']\n", "    if all(col in learner_section_attempts_agg.columns for col in required_cols):\n", "        \n", "        # 1. Individual section analysis\n", "        print('=== INDIVIDUAL SECTION ANALYSIS ===')\n", "        \n", "        # Get a sample section for detailed analysis\n", "        if 'section_id' in sections.columns and len(sections) > 0:\n", "            sample_section_id = sections['section_id'].iloc[0]\n", "            section_data = learner_section_attempts_agg[\n", "                learner_section_attempts_agg['section_id'] == sample_section_id\n", "            ]\n", "            \n", "            if len(section_data) > 0:\n", "                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "                \n", "                # Scatter plot\n", "                ax1.scatter(section_data['attempts'], section_data['mastery'], \n", "                           alpha=0.6, s=50, color='steelblue')\n", "                ax1.set_title(f'Mastery vs. Attempts\\nSection {sample_section_id}')\n", "                ax1.set_xlabel('Number of Attempts')\n", "                ax1.set_ylabel('Mastery Score')\n", "                ax1.grid(True, alpha=0.3)\n", "                \n", "                # Add trend line\n", "                z = np.polyfit(section_data['attempts'], section_data['mastery'], 1)\n", "                p = np.poly1d(z)\n", "                ax1.plot(section_data['attempts'], p(section_data['attempts']), \n", "                        'r--', alpha=0.8, label=f'Trend line')\n", "                ax1.legend()\n", "                \n", "                # Distribution of attempts\n", "                ax2.hist(section_data['attempts'], bins=20, alpha=0.7, color='lightcoral')\n", "                ax2.set_title(f'Distribution of Attempts\\nSection {sample_section_id}')\n", "                ax2.set_xlabel('Number of Attempts')\n", "                ax2.set_ylabel('Frequency')\n", "                ax2.grid(True, alpha=0.3)\n", "                \n", "                plt.tight_layout()\n", "                plt.show()\n", "                \n", "                print(f'Section {sample_section_id} Statistics:')\n", "                print(f'- Total learners: {len(section_data)}')\n", "                print(f'- Average attempts: {section_data[\"attempts\"].mean():.2f}')\n", "                print(f'- Average mastery: {section_data[\"mastery\"].mean():.2f}')\n", "                print(f'- Correlation (attempts vs mastery): {section_data[\"attempts\"].corr(section_data[\"mastery\"]):.3f}')\n", "        \n", "        # 2. Aggregate analysis across all sections\n", "        print('\\n=== AGGREGATE ANALYSIS ACROSS ALL SECTIONS ===')\n", "        \n", "        aggregated_data = learner_section_attempts_agg.groupby('section_id').agg({\n", "            'attempts': ['mean', 'std', 'count'],\n", "            'mastery': ['mean', 'std']\n", "        }).round(3)\n", "        \n", "        # Flatten column names\n", "        aggregated_data.columns = ['_'.join(col).strip() for col in aggregated_data.columns]\n", "        aggregated_data = aggregated_data.reset_index()\n", "        \n", "        # Create comprehensive visualization\n", "        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "        \n", "        # 1. Average mastery vs average attempts\n", "        scatter = ax1.scatter(aggregated_data['attempts_mean'], \n", "                            aggregated_data['mastery_mean'],\n", "                            s=aggregated_data['attempts_count']*2,\n", "                            alpha=0.6, c=aggregated_data['mastery_mean'], \n", "                            cmap='RdYlBu_r')\n", "        ax1.set_title('Average Mastery vs. Average Attempts\\n(Size = Number of Learners)')\n", "        ax1.set_xlabel('Average Number of Attempts')\n", "        ax1.set_ylabel('Average Mastery Score')\n", "        ax1.grid(True, alpha=0.3)\n", "        plt.colorbar(scatter, ax=ax1, label='Mastery Score')\n", "        \n", "        # 2. Distribution of average attempts\n", "        ax2.hist(aggregated_data['attempts_mean'], bins=20, alpha=0.7, color='skyblue')\n", "        ax2.set_title('Distribution of Average Attempts per Section')\n", "        ax2.set_xlabel('Average Attempts')\n", "        ax2.set_ylabel('Number of Sections')\n", "        ax2.grid(True, alpha=0.3)\n", "        \n", "        # 3. Distribution of average mastery\n", "        ax3.hist(aggregated_data['mastery_mean'], bins=20, alpha=0.7, color='lightgreen')\n", "        ax3.set_title('Distribution of Average Mastery per Section')\n", "        ax3.set_xlabel('Average Mastery Score')\n", "        ax3.set_ylabel('Number of Sections')\n", "        ax3.grid(True, alpha=0.3)\n", "        \n", "        # 4. Attempts vs Mastery correlation by section\n", "        section_correlations = []\n", "        for section_id in aggregated_data['section_id']:\n", "            section_subset = learner_section_attempts_agg[\n", "                learner_section_attempts_agg['section_id'] == section_id\n", "            ]\n", "            if len(section_subset) > 1:\n", "                corr = section_subset['attempts'].corr(section_subset['mastery'])\n", "                section_correlations.append(corr if not np.isnan(corr) else 0)\n", "            else:\n", "                section_correlations.append(0)\n", "        \n", "        ax4.hist(section_correlations, bins=20, alpha=0.7, color='orange')\n", "        ax4.set_title('Distribution of Attempts-Mastery Correlations\\nAcross Sections')\n", "        ax4.set_xlabel('Correlation Coefficient')\n", "        ax4.set_ylabel('Number of Sections')\n", "        ax4.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='Zero correlation')\n", "        ax4.grid(True, alpha=0.3)\n", "        ax4.legend()\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # 3. Identify problematic sections\n", "        print('\\n=== PROBLEMATIC SECTIONS IDENTIFICATION ===')\n", "        \n", "        high_attempts_threshold = aggregated_data['attempts_mean'].quantile(0.75)\n", "        low_mastery_threshold = aggregated_data['mastery_mean'].quantile(0.25)\n", "        \n", "        problematic_sections = aggregated_data[\n", "            (aggregated_data['attempts_mean'] > high_attempts_threshold) & \n", "            (aggregated_data['mastery_mean'] < low_mastery_threshold)\n", "        ]\n", "        \n", "        print(f'Thresh<PERSON>s used:')\n", "        print(f'- High attempts threshold (75th percentile): {high_attempts_threshold:.2f}')\n", "        print(f'- Low mastery threshold (25th percentile): {low_mastery_threshold:.2f}')\n", "        print(f'\\nProblematic sections found: {len(problematic_sections)}')\n", "        \n", "        if len(problematic_sections) > 0:\n", "            display(problematic_sections[['section_id', 'attempts_mean', 'mastery_mean', 'attempts_count']])\n", "        else:\n", "            print('No sections meet the problematic criteria.')\n", "            \n", "    else:\n", "        print(f'Required columns not found. Available columns: {list(learner_section_attempts_agg.columns)}')\n", "else:\n", "    print('Required datasets not available for analysis.')"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Identify Potentially Problematic Sections"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Identify sections where users tend to get stuck or where mastery doesn’t improve\n", "# Correlate these findings with section difficulty, user feedback, or other possible factors\n", "\n", "# Example: Identifying sections with low mastery scores after multiple attempts\n", "threshold_attempts = 5  # Define a threshold for the number of attempts\n", "low_mastery_threshold = 0.5  # Define a threshold for low mastery\n", "\n", "problematic_sections = learner_section_attempts_agg[\n", "    (learner_section_attempts_agg['attempts'] > threshold_attempts) & \n", "    (learner_section_attempts_agg['mastery'] < low_mastery_threshold)\n", "]\n", "\n", "print(\"Problematic Sections (Individual Data):\")\n", "print(problematic_sections[['section_id', 'attempts', 'mastery']].head())\n", "\n", "# Further analysis: Identify sections with a significant difference between attempts and mastery\n", "learner_section_attempts_agg['attempts_mastery_diff'] = learner_section_attempts_agg['attempts'] - learner_section_attempts_agg['mastery']\n", "\n", "significant_diff_threshold = learner_section_attempts_agg['attempts_mastery_diff'].quantile(0.9)  # 90th percentile\n", "\n", "problematic_sections_diff = learner_section_attempts_agg[\n", "    learner_section_attempts_agg['attempts_mastery_diff'] > significant_diff_threshold\n", "]\n", "\n", "print(\"Problematic Sections (Significant Difference between Attempts and Mastery):\")\n", "print(problematic_sections_diff[['section_id', 'attempts', 'mastery', 'attempts_mastery_diff']].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enhanced Analysis and Insights"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Enhanced analysis with better error handling and insights\n", "if learner_section_attempts_agg is not None:\n", "    print('=== ENHANCED ANALYSIS AND INSIGHTS ===')\n", "    \n", "    # Check data availability\n", "    required_cols = ['section_id', 'attempts', 'mastery']\n", "    if all(col in learner_section_attempts_agg.columns for col in required_cols):\n", "        \n", "        # Calculate learning efficiency metrics\n", "        df_analysis = learner_section_attempts_agg.copy()\n", "        df_analysis['efficiency'] = df_analysis['mastery'] / df_analysis['attempts']\n", "        df_analysis['efficiency'] = df_analysis['efficiency'].replace([np.inf, -np.inf], np.nan)\n", "        \n", "        # Section-level insights\n", "        section_insights = df_analysis.groupby('section_id').agg({\n", "            'attempts': ['count', 'mean', 'median', 'std'],\n", "            'mastery': ['mean', 'median', 'std'],\n", "            'efficiency': ['mean', 'median']\n", "        }).round(3)\n", "        \n", "        section_insights.columns = ['learners', 'avg_attempts', 'med_attempts', 'std_attempts',\n", "                                   'avg_mastery', 'med_mastery', 'std_mastery', \n", "                                   'avg_efficiency', 'med_efficiency']\n", "        section_insights = section_insights.reset_index()\n", "        \n", "        # Identify different types of problematic sections\n", "        high_effort_low_mastery = section_insights[\n", "            (section_insights['avg_attempts'] > section_insights['avg_attempts'].quantile(0.75)) &\n", "            (section_insights['avg_mastery'] < section_insights['avg_mastery'].quantile(0.25))\n", "        ]\n", "        \n", "        low_efficiency = section_insights[\n", "            section_insights['avg_efficiency'] < section_insights['avg_efficiency'].quantile(0.1)\n", "        ]\n", "        \n", "        high_variability = section_insights[\n", "            (section_insights['std_attempts'] > section_insights['std_attempts'].quantile(0.9)) |\n", "            (section_insights['std_mastery'] > section_insights['std_mastery'].quantile(0.9))\n", "        ]\n", "        \n", "        # Display insights\n", "        print(f'\\nSECTION CATEGORIES:')\n", "        print(f'High Effort, Low Mastery sections: {len(high_effort_low_mastery)}')\n", "        print(f'Low Efficiency sections: {len(low_efficiency)}')\n", "        print(f'High Variability sections: {len(high_variability)}')\n", "        \n", "        if len(high_effort_low_mastery) > 0:\n", "            print('\\nHigh <PERSON>, Low Mastery Sections:')\n", "            display(high_effort_low_mastery[['section_id', 'learners', 'avg_attempts', 'avg_mastery', 'avg_efficiency']].head())\n", "        \n", "        # Create comprehensive visualization\n", "        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "        \n", "        # 1. Efficiency distribution\n", "        valid_efficiency = df_analysis['efficiency'].dropna()\n", "        ax1.hist(valid_efficiency, bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n", "        ax1.axvline(valid_efficiency.mean(), color='red', linestyle='--', \n", "                   label=f'Mean: {valid_efficiency.mean():.3f}')\n", "        ax1.axvline(valid_efficiency.median(), color='orange', linestyle='--', \n", "                   label=f'Median: {valid_efficiency.median():.3f}')\n", "        ax1.set_xlabel('Learning Efficiency (Mastery/Attempts)')\n", "        ax1.set_ylabel('Frequency')\n", "        ax1.set_title('Distribution of Learning Efficiency')\n", "        ax1.legend()\n", "        ax1.grid(True, alpha=0.3)\n", "        \n", "        # 2. Section performance scatter\n", "        scatter = ax2.scatter(section_insights['avg_attempts'], section_insights['avg_mastery'],\n", "                            s=section_insights['learners']*3, alpha=0.6,\n", "                            c=section_insights['avg_efficiency'], cmap='RdYlGn')\n", "        ax2.set_xlabel('Average Attempts')\n", "        ax2.set_ylabel('Average Mastery')\n", "        ax2.set_title('Section Performance Overview\\n(Size=Learners, Color=Efficiency)')\n", "        ax2.grid(True, alpha=0.3)\n", "        plt.colorbar(scatter, ax=ax2, label='Avg Efficiency')\n", "        \n", "        # 3. Attempts vs Mastery relationship\n", "        ax3.scatter(df_analysis['attempts'], df_analysis['mastery'], alpha=0.4, s=20)\n", "        \n", "        # Add trend line\n", "        z = np.polyfit(df_analysis['attempts'], df_analysis['mastery'], 1)\n", "        p = np.poly1d(z)\n", "        ax3.plot(df_analysis['attempts'], p(df_analysis['attempts']), 'r--', alpha=0.8)\n", "        \n", "        correlation = df_analysis['attempts'].corr(df_analysis['mastery'])\n", "        ax3.set_xlabel('Attempts')\n", "        ax3.set_ylabel('Mastery')\n", "        ax3.set_title(f'Attempts vs Mastery Relationship\\n(Correlation: {correlation:.3f})')\n", "        ax3.grid(True, alpha=0.3)\n", "        \n", "        # 4. Box plot of mastery by attempt ranges\n", "        df_analysis['attempt_range'] = pd.cut(df_analysis['attempts'], \n", "                                            bins=[0, 2, 5, 10, float('inf')],\n", "                                            labels=['1-2', '3-5', '6-10', '10+'])\n", "        \n", "        sns.boxplot(data=df_analysis, x='attempt_range', y='mastery', ax=ax4)\n", "        ax4.set_xlabel('Attempt Range')\n", "        ax4.set_ylabel('Mastery Score')\n", "        ax4.set_title('Mastery Distribution by Attempt Range')\n", "        ax4.grid(True, alpha=0.3)\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # Summary insights\n", "        print('\\nKEY INSIGHTS:')\n", "        print(f'• Overall correlation between attempts and mastery: {correlation:.3f}')\n", "        print(f'• Average learning efficiency: {valid_efficiency.mean():.3f}')\n", "        print(f'• {len(df_analysis[df_analysis[\"attempts\"] > 10])} learners required >10 attempts')\n", "        print(f'• {len(df_analysis[df_analysis[\"mastery\"] < 0.3])} learners achieved <30% mastery')\n", "        \n", "        # Recommendations\n", "        print('\\nRECOMMENDATIONS:')\n", "        if correlation < 0:\n", "            print('• Negative correlation suggests more attempts may not lead to better mastery')\n", "            print('• Consider reviewing instructional design and feedback mechanisms')\n", "        elif correlation < 0.3:\n", "            print('• Weak positive correlation suggests room for improvement in learning efficiency')\n", "            print('• Consider adaptive learning paths or personalized interventions')\n", "        \n", "        if len(high_effort_low_mastery) > 0:\n", "            print(f'• {len(high_effort_low_mastery)} sections show high effort but low mastery - priority for review')\n", "        \n", "        if len(high_variability) > 0:\n", "            print(f'• {len(high_variability)} sections show high variability - may need differentiated instruction')\n", "            \n", "    else:\n", "        print(f'Required columns missing. Available: {list(learner_section_attempts_agg.columns)}')\n", "else:\n", "    print('Dataset not available for enhanced analysis.')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Deliverables"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary and Conclusions\n", "\n", "### Key Findings:\n", "1. Enhanced data loading with error handling implemented\n", "2. Comprehensive visualization of mastery vs. attempts relationships\n", "3. Multi-level analysis (individual and section-level) completed\n", "4. Learning efficiency metrics calculated and analyzed\n", "5. Problematic sections identified using multiple criteria\n", "\n", "### Recommendations:\n", "1. Focus intervention efforts on sections with high attempt-to-mastery ratios\n", "2. Implement adaptive learning paths for struggling learners\n", "3. Review instructional design for sections with negative attempt-mastery correlation\n", "4. Provide additional support for sections with high performance variability\n", "5. Monitor learning efficiency trends over time\n", "\n", "### Assumptions and Limitations:\n", "- Datasets are complete and accurate\n", "- Mastery scores reliably indicate understanding\n", "- Higher attempts with low mastery indicates learning difficulties\n", "- Section-level aggregations provide meaningful insights\n", "- Analysis assumes consistent measurement across sections\n", "\n", "### Next Steps:\n", "1. Validate findings with subject matter experts\n", "2. Implement targeted interventions for identified problematic sections\n", "3. Set up monitoring dashboard for ongoing analysis\n", "4. Conduct longitudinal analysis to track improvement\n", "5. <PERSON><PERSON> qualitative feedback from learners and instructors"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}