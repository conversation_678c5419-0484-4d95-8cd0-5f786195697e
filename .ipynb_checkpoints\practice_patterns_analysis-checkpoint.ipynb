{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named '<PERSON><PERSON><PERSON><PERSON><PERSON>'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mC<PERSON>\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Import libraries\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m \u001b[34;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[34;01mpd\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[34;01mmatplotlib\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpyplot\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[34;01mplt\u001b[39;00m\n\u001b[32m      4\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[34;01<PERSON><PERSON>born\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[34;01msns\u001b[39;00m\n\u001b[32m      6\u001b[39m \u001b[38;5;66;03m# Set style for plots\u001b[39;00m\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'matplotlib'"]}], "source": ["# Import libraries\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Set style for plots\n", "sns.set_style('whitegrid')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Datasets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the datasets\n", "all_attempts = pd.read_csv('all_attempts.csv')\n", "attempts = pd.read_csv('attempts.csv')\n", "learner_attempts_agg = pd.read_csv('learner_attempts_agg.csv')\n", "learner_prompts_agg = pd.read_csv('learner_prompts_agg.csv')\n", "learner_section_attempts_agg = pd.read_csv('learner_section_attempts_agg.csv')\n", "learner_section_prompts_agg = pd.read_csv('learner_section_prompts_agg.csv')\n", "learner_sectiontype_attempts_agg = pd.read_csv('learner_sectiontype_attempts_agg.csv')\n", "learner_sectiontype_with_masteries_agg = pd.read_csv('learner_sectiontype_with_masteries_agg.csv')\n", "learner_with_masteries_agg = pd.read_csv('learner_with_masteries_agg.csv')\n", "learners = pd.read_csv('learners.csv')\n", "prompts = pd.read_csv('prompts.csv')\n", "section_mapping = pd.read_csv('section_mapping.csv')\n", "sections = pd.read_csv('sections.csv')\n", "struggling_learner_sections = pd.read_csv('struggling_learner_sections.csv')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Investigate Trajectories of Mastery vs. Questions Attempted"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze how users progress through various sections\n", "# Plot trajectories for mastery vs. questions attempted for each section\n", "# Identify patterns or anomalies in user progress\n", "\n", "# Example: Plotting mastery vs. questions attempted for a specific section\n", "section_id = sections['section_id'].iloc[0] # Select the first section_id\n", "section_data = learner_section_attempts_agg[learner_section_attempts_agg['section_id'] == section_id]\n", "\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(section_data['attempts'], section_data['mastery'], alpha=0.5)\n", "plt.title(f'Mastery vs. Attempts for Section {section_id}')\n", "plt.xlabel('Number of Attempts')\n", "plt.ylabel('Mastery Score')\n", "plt.show()\n", "\n", "# Further analysis: Aggregate data to plot average mastery vs. attempts for all sections\n", "aggregated_data = learner_section_attempts_agg.groupby('section_id').agg({'attempts': 'mean', 'mastery': 'mean'}).reset_index()\n", "\n", "plt.figure(figsize=(12, 8))\n", "plt.scatter(aggregated_data['attempts'], aggregated_data['mastery'], alpha=0.5)\n", "plt.title('Average Mastery vs. Average Attempts for All Sections')\n", "plt.xlabel('Average Number of Attempts')\n", "plt.ylabel('Average Mastery Score')\n", "plt.show()\n", "\n", "# Identify sections with a high number of attempts but low average mastery\n", "high_attempts_threshold = aggregated_data['attempts'].quantile(0.75)  # 75th percentile\n", "low_mastery_threshold = 0.5\n", "\n", "problematic_sections_aggregated = aggregated_data[\n", "    (aggregated_data['attempts'] > high_attempts_threshold) & \n", "    (aggregated_data['mastery'] < low_mastery_threshold)\n", "]\n", "\n", "print(\"Problematic Sections (Aggregated Data):\")\n", "print(problematic_sections_aggregated)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Identify Potentially Problematic Sections"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Identify sections where users tend to get stuck or where mastery doesn’t improve\n", "# Correlate these findings with section difficulty, user feedback, or other possible factors\n", "\n", "# Example: Identifying sections with low mastery scores after multiple attempts\n", "threshold_attempts = 5  # Define a threshold for the number of attempts\n", "low_mastery_threshold = 0.5  # Define a threshold for low mastery\n", "\n", "problematic_sections = learner_section_attempts_agg[\n", "    (learner_section_attempts_agg['attempts'] > threshold_attempts) & \n", "    (learner_section_attempts_agg['mastery'] < low_mastery_threshold)\n", "]\n", "\n", "print(\"Problematic Sections (Individual Data):\")\n", "print(problematic_sections[['section_id', 'attempts', 'mastery']].head())\n", "\n", "# Further analysis: Identify sections with a significant difference between attempts and mastery\n", "learner_section_attempts_agg['attempts_mastery_diff'] = learner_section_attempts_agg['attempts'] - learner_section_attempts_agg['mastery']\n", "\n", "significant_diff_threshold = learner_section_attempts_agg['attempts_mastery_diff'].quantile(0.9)  # 90th percentile\n", "\n", "problematic_sections_diff = learner_section_attempts_agg[\n", "    learner_section_attempts_agg['attempts_mastery_diff'] > significant_diff_threshold\n", "]\n", "\n", "print(\"Problematic Sections (Significant Difference between Attempts and Mastery):\")\n", "print(problematic_sections_diff[['section_id', 'attempts', 'mastery', 'attempts_mastery_diff']].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Deliverables"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Assumptions:\n", "# 1. The datasets are complete and accurate.\n", "# 2. The mastery scores are a reliable indicator of user understanding.\n", "# 3. A higher number of attempts with low mastery indicates a problematic section.\n", "# 4. Sections with high average attempts and low average mastery are considered problematic.\n", "# 5. Sections with a significant difference between attempts and mastery are considered problematic."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}